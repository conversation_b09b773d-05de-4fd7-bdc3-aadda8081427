from pathlib import Path

import numpy as np
import pandas as pd

# 导入 logwp 核心库
from logwp.io import WpExcelReader, WpExcelWriter
from logwp.models.well_project import WpWellProject
from logwp.models.datasets.bundle import WpDataFrameBundle

from logwp.extras.tracking import RunContext
from logwp.extras.plotting import registry as plot_registry

from logwp.extras.petroplot.common import *

from logwp.extras.petroplot.nmr_ternary import create_publication_ready_perm_config as nmr_ternary_create_publication_ready_perm_config
from logwp.extras.petroplot.nmr_ternary import run_nmr_ternary_plot_step, NmrTernaryDataSelectors

from logwp.extras.petroplot.crossplot import create_publication_ready_perm_config as crossplot_create_publication_ready_perm_config
from logwp.extras.petroplot.crossplot import run_crossplot_step, CrossPlotConfig

pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', 50)

print("===库导入完成!")

# --- 加载WL数据 ---
from logwp.models.constants import WpDepthRole


wl_data_file_path = "./nmr_ternary.wp.xlsx"
reader = WpExcelReader()
wl_project = reader.read(wl_data_file_path)
print(f"✅ 成功读取WL数据: {wl_data_file_path}")

wl_bundle = wl_project.get_dataset("nmr_ternary").extract_curve_dataframe_bundle(
    include_system_columns=True
)
wl_well_name = wl_bundle.curve_metadata.get_well_identifier_curves()[0]
wl_depth_name = wl_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]

print (f"===WL Bundle: {wl_bundle.data.head()}")

# --- 加载LWD数据 ---
lwd_data_file_path = "./lwd_nmr_ternary.wp.xlsx"
lwd_project = reader.read(lwd_data_file_path)
print(f"✅ 成功读取LWD数据: {lwd_data_file_path}")

lwd_bundle = lwd_project.get_dataset("lwd_nmr_ternary").extract_curve_dataframe_bundle(
    include_system_columns=True
)
lwd_well_name = lwd_bundle.curve_metadata.get_well_identifier_curves()[0]
lwd_depth_name = lwd_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]

print (f"===LWD Bundle: {lwd_bundle.data.head()}")

# --- 加载CrossPlot数据 ---
crossplot_data_file_path = "./crossplot.wp.xlsx"
crossplot_project = reader.read(crossplot_data_file_path)
print(f"✅ 成功读取crossplot数据: {crossplot_data_file_path}")

crossplot_bundle = crossplot_project.get_dataset("crossplot").extract_curve_dataframe_bundle(
    include_system_columns=True
)
crossplot_well_name = crossplot_bundle.curve_metadata.get_well_identifier_curves()[0]
crossplot_depth_name = crossplot_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]

print (f"===Crossplot Bundle: {crossplot_bundle.data.head()}")

# 初始化RunContext
output_dir = Path("./output01")
run_dir_name = RunContext.generate_timestamped_run_name(prefix="plot")
run_context = RunContext(output_dir / run_dir_name,overwrite=True)
print(f"实验运行已初始化，所有产物将保存至: {run_context.run_dir.resolve()}")

plot_config, plot_profile = nmr_ternary_create_publication_ready_perm_config(
    enable_background=True,
    colorscale='Plasma',
    font_size_pt=20,
    tick_font_size_pt=20,
    marker_size=18,
    legend_position='right')

wl_selectors = NmrTernaryDataSelectors(
    macro_curve="VMACRO",
    micro_curve="VMICRO",
    meso_curve="VMESO",
    color_curve="K_LABEL",
    hover_extra_curves=[wl_well_name, wl_depth_name]
)

wl_results = run_nmr_ternary_plot_step(
    config=plot_config,
    selectors=wl_selectors,
    ctx=run_context,
    bundle=wl_bundle,
    plot_profile=plot_profile,
    prefix="WL"
)

lwd_selectors = NmrTernaryDataSelectors(
    macro_curve="VMACRO_LWD",
    micro_curve="VMICRO_LWD",
    meso_curve="VMESO_LWD",
    color_curve="K_LABEL",
    hover_extra_curves=[wl_well_name, wl_depth_name]
)

lwd_results = run_nmr_ternary_plot_step(
    config=plot_config,
    selectors=lwd_selectors,
    ctx=run_context,
    bundle=lwd_bundle,
    plot_profile=plot_profile,
    prefix="LWD"
)

plot_config, plot_profile = crossplot_create_publication_ready_perm_config(
    bundle_name="main",
    x_curve="RD_LWD",
    y_curve="RS_LWD",
    color_curve="K_LABEL",
    x_title="RD_LWD(ohm.m)",
    y_title="RS_LWD(ohm.m)",
    x_range=(0.2,2000),
    y_range=(0.2,2000),
    x_log=True,
    y_log=True,
    show_marginal_x=True,
    show_marginal_y=True,
    show_diagonal_line=True,
    title = None,
    colorscale= "Plasma",
    cmin = -2.0,
    cmax = 3.0,
    distinguish_null_color =True,
)

results = run_crossplot_step(
    config=plot_config,
    ctx=run_context,
    prefix="rd_rs_lwd",
    bundles={
        "main": crossplot_bundle
    },
    plot_profile=plot_profile
)

run_context.finalize()