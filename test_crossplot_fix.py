#!/usr/bin/env python3
"""
测试crossplot修复的脚本
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from logwp.extras.petroplot.crossplot import crossplot_create_publication_ready_perm_config, run_crossplot_step
from logwp.models import WpArrayBundle

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    n_points = 500
    
    # 创建对数正态分布的数据，适合对数刻度
    x_data = np.random.lognormal(mean=0, sigma=1, size=n_points)
    y_data = np.random.lognormal(mean=0, sigma=1, size=n_points)
    
    # 创建颜色数据（渗透率的对数值）
    color_data = np.random.normal(loc=0, scale=1.5, size=n_points)
    
    # 创建DataFrame
    df = pd.DataFrame({
        'WELL_NAME': ['TEST_WELL'] * n_points,
        'DEPTH': np.linspace(1000, 2000, n_points),
        'RD_LWD': x_data,
        'RS_LWD': y_data,
        'K_LABEL': color_data
    })
    
    return df

def create_test_bundle(df):
    """创建测试用的WpArrayBundle"""
    arrays = {}
    for col in df.columns:
        arrays[col] = df[col].values
    
    return WpArrayBundle(
        arrays=arrays,
        well_curve_name='WELL_NAME',
        depth_curve_name='DEPTH'
    )

def test_crossplot_fix():
    """测试crossplot修复"""
    print("=== 测试crossplot修复 ===")
    
    # 1. 创建测试数据
    print("1. 创建测试数据...")
    df = create_test_data()
    bundle = create_test_bundle(df)
    print(f"   数据点数: {len(df)}")
    print(f"   X轴范围: {df['RD_LWD'].min():.3f} - {df['RD_LWD'].max():.3f}")
    print(f"   Y轴范围: {df['RS_LWD'].min():.3f} - {df['RS_LWD'].max():.3f}")
    
    # 2. 创建配置
    print("2. 创建crossplot配置...")
    plot_config, plot_profile = crossplot_create_publication_ready_perm_config(
        bundle_name="main",
        x_curve="RD_LWD",
        y_curve="RS_LWD",
        color_curve="K_LABEL",
        x_title="RD_LWD(ohm.m)",
        y_title="RS_LWD(ohm.m)",
        x_range=(0.2, 20),
        y_range=(0.2, 20),
        x_log=True,
        y_log=True,
        show_marginal_x=True,
        show_marginal_y=True,
        show_diagonal_line=True,
        title="测试交会图",
        colorscale="Plasma",
        cmin=-2.0,
        cmax=3.0,
        distinguish_null_color=True,
    )
    
    print("   配置创建成功")
    print(f"   X轴对数刻度: {plot_config.x_axis.scale}")
    print(f"   Y轴对数刻度: {plot_config.y_axis.scale}")
    print(f"   显示X轴边缘图: {plot_config.marginal_x.show if plot_config.marginal_x else False}")
    print(f"   显示Y轴边缘图: {plot_config.marginal_y.show if plot_config.marginal_y else False}")
    
    # 3. 创建运行上下文
    class MockRunContext:
        def __init__(self):
            self.output_dir = Path("./test_output")
            self.output_dir.mkdir(exist_ok=True)
    
    run_context = MockRunContext()
    
    # 4. 运行crossplot
    print("3. 运行crossplot...")
    try:
        results = run_crossplot_step(
            config=plot_config,
            ctx=run_context,
            prefix="test_rd_rs_lwd",
            bundles={
                "main": bundle
            },
            plot_profile=plot_profile
        )
        
        print("   crossplot运行成功!")
        print(f"   输出文件: {results}")
        
        # 5. 验证输出
        if results and Path(results).exists():
            print(f"   图片文件已生成: {results}")
            print(f"   文件大小: {Path(results).stat().st_size} bytes")
        else:
            print("   警告: 图片文件未生成或路径不正确")
            
    except Exception as e:
        print(f"   错误: crossplot运行失败 - {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("=== 测试完成 ===")
    return True

if __name__ == "__main__":
    success = test_crossplot_fix()
    sys.exit(0 if success else 1)
