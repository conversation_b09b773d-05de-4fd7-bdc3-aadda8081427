# Plotly 6.0+ API 使用指南

## 概述

本文档为SCAPE项目中使用Plotly 6.0+版本的API使用指南，重点关注交会图（crossplot）和边缘图（marginal plots）的实现。

## 核心API变化

### 1. 字体配置系统

#### 全局字体设置
```python
# 推荐方式：使用 update_layout 设置全局字体
fig.update_layout(
    font=dict(
        family="Arial, sans-serif",  # 字体族
        size=12,                     # 字体大小
        color="black"                # 字体颜色
    )
)
```

#### 特定元素字体设置
```python
# 标题字体
fig.update_layout(
    title=dict(
        text="图表标题",
        font=dict(
            family="Times New Roman",
            size=18,
            color="red"
        )
    )
)

# 坐标轴标题字体
fig.update_xaxes(title_font=dict(family="Arial", size=14, color="blue"))
fig.update_yaxes(title_font=dict(family="Arial", size=14, color="blue"))

# 刻度标签字体
fig.update_xaxes(tickfont=dict(family="Courier", size=10))
fig.update_yaxes(tickfont=dict(family="Courier", size=10))
```

### 2. 子图布局系统

#### make_subplots 基础用法
```python
from plotly.subplots import make_subplots

# 创建2x2子图布局
fig = make_subplots(
    rows=2, cols=2,
    shared_xaxes=True,      # 共享X轴
    shared_yaxes=True,      # 共享Y轴
    vertical_spacing=0.02,   # 垂直间距
    horizontal_spacing=0.02, # 水平间距
    subplot_titles=("Plot 1", "Plot 2", "Plot 3", "Plot 4")
)
```

#### 边缘图布局（Marginal Plots）
```python
# 创建带边缘图的复杂布局
fig = make_subplots(
    rows=2, cols=2,
    column_widths=[0.8, 0.2],    # 主图80%，边缘图20%
    row_heights=[0.2, 0.8],      # 边缘图20%，主图80%
    shared_xaxes=True,
    shared_yaxes=True,
    vertical_spacing=0.02,
    horizontal_spacing=0.02
)

# 主图位置：(2, 1)
# X轴边缘图位置：(1, 1)  
# Y轴边缘图位置：(2, 2)
```

### 3. 坐标轴配置

#### 基础坐标轴设置
```python
# 设置坐标轴类型和范围
fig.update_xaxes(
    type="log",              # 对数刻度
    range=[0.1, 1000],       # 范围设置
    title="X轴标题",
    showgrid=True,           # 显示网格线
    gridcolor="lightgray",   # 网格线颜色
    gridwidth=1              # 网格线宽度
)

fig.update_yaxes(
    type="log",
    range=[0.1, 1000],
    title="Y轴标题",
    showgrid=True,
    gridcolor="lightgray",
    gridwidth=1
)
```

#### 子图中的坐标轴配置
```python
# 为特定子图设置坐标轴
fig.update_xaxes(
    title="主图X轴",
    type="log",
    row=2, col=1  # 指定子图位置
)

fig.update_yaxes(
    title="主图Y轴", 
    type="log",
    row=2, col=1
)

# 隐藏边缘图的坐标轴标签
fig.update_xaxes(
    showticklabels=False,
    title="",
    row=1, col=1  # X轴边缘图
)

fig.update_yaxes(
    showticklabels=False,
    title="",
    row=2, col=2  # Y轴边缘图
)
```

### 4. 轨迹添加和样式

#### 添加散点图轨迹
```python
# 添加到主图
fig.add_trace(
    go.Scatter(
        x=x_data,
        y=y_data,
        mode='markers',
        marker=dict(
            size=8,
            color=color_data,
            colorscale='Viridis',
            showscale=True,
            colorbar=dict(title="颜色标题")
        ),
        name="数据点"
    ),
    row=2, col=1  # 主图位置
)
```

#### 添加边缘分布图
```python
# X轴边缘直方图
fig.add_trace(
    go.Histogram(
        x=x_data,
        nbinsx=30,
        marker_color='lightblue',
        showlegend=False,
        name=""
    ),
    row=1, col=1
)

# Y轴边缘直方图
fig.add_trace(
    go.Histogram(
        y=y_data,
        nbinsy=30,
        marker_color='lightcoral',
        showlegend=False,
        name="",
        orientation='h'  # 水平方向
    ),
    row=2, col=2
)
```

### 5. 颜色轴和图例

#### 全局颜色轴配置
```python
fig.update_layout(
    coloraxis=dict(
        colorscale='Plasma',
        cmin=-2.0,
        cmax=3.0,
        colorbar=dict(
            title="渗透率 (log mD)",
            titleside="right",
            tickfont=dict(size=10),
            titlefont=dict(size=12)
        )
    )
)
```

#### 图例配置
```python
fig.update_layout(
    legend=dict(
        x=0.02,
        y=0.98,
        bgcolor="rgba(255,255,255,0.8)",
        bordercolor="black",
        borderwidth=1,
        font=dict(size=10)
    ),
    showlegend=True
)
```

## 常见问题和解决方案

### 1. 字体不显示问题
**问题**：设置的字体样式不生效
**解决方案**：
```python
# 确保在 update_layout 中设置全局字体
fig.update_layout(
    font=dict(family="Arial", size=12),
    # 同时设置特定元素的字体
    title_font=dict(size=16),
    legend_font=dict(size=10)
)
```

### 2. 对数刻度不生效
**问题**：设置 `type="log"` 后坐标轴仍为线性刻度
**解决方案**：
```python
# 确保数据中没有零值或负值
# 对数刻度要求所有数据 > 0
fig.update_xaxes(
    type="log",
    range=[np.log10(0.1), np.log10(1000)]  # 使用log10值设置范围
)
```

### 3. 边缘图坐标轴错乱
**问题**：边缘图的坐标轴标题显示在错误位置
**解决方案**：
```python
# 明确指定每个子图的坐标轴配置
# 主图坐标轴
fig.update_xaxes(title="X轴标题", row=2, col=1)
fig.update_yaxes(title="Y轴标题", row=2, col=1)

# 边缘图坐标轴（隐藏标题和刻度标签）
fig.update_xaxes(showticklabels=False, title="", row=1, col=1)
fig.update_yaxes(showticklabels=False, title="", row=2, col=2)
```

## 最佳实践

### 1. 布局设置顺序
```python
# 推荐的设置顺序
fig = make_subplots(...)           # 1. 创建子图布局
fig.add_trace(...)                 # 2. 添加数据轨迹
fig.update_xaxes(...)             # 3. 配置坐标轴
fig.update_yaxes(...)
fig.update_layout(...)            # 4. 设置全局布局
```

### 2. 性能优化
```python
# 批量更新而非逐个设置
layout_updates = {
    'font': dict(family="Arial", size=12),
    'title': dict(text="标题", font=dict(size=16)),
    'showlegend': True,
    'width': 800,
    'height': 600
}
fig.update_layout(**layout_updates)
```

### 3. 响应式设计
```python
# 使用相对尺寸而非固定像素
fig.update_layout(
    width=None,   # 自适应宽度
    height=600,   # 固定高度
    autosize=True # 自动调整大小
)
```

## 参考资源

- [Plotly Python 官方文档](https://plotly.com/python/)
- [子图布局指南](https://plotly.com/python/subplots/)
- [坐标轴配置](https://plotly.com/python/axes/)
- [字体和标签设置](https://plotly.com/python/figure-labels/)
