# Plotly 6.0+ API 使用指南

## 概述

本文档为SCAPE项目中使用Plotly 6.0+版本的API使用指南，重点关注交会图（crossplot）和边缘图（marginal plots）的实现。

## 核心API变化

### 1. 字体配置系统

#### 全局字体设置
```python
# 推荐方式：使用 update_layout 设置全局字体
fig.update_layout(
    font=dict(
        family="Arial, sans-serif",  # 字体族
        size=12,                     # 字体大小
        color="black"                # 字体颜色
    )
)
```

#### 特定元素字体设置
```python
# 标题字体
fig.update_layout(
    title=dict(
        text="图表标题",
        font=dict(
            family="Times New Roman",
            size=18,
            color="red"
        )
    )
)

# 坐标轴标题字体
fig.update_xaxes(title_font=dict(family="Arial", size=14, color="blue"))
fig.update_yaxes(title_font=dict(family="Arial", size=14, color="blue"))

# 刻度标签字体
fig.update_xaxes(tickfont=dict(family="Courier", size=10))
fig.update_yaxes(tickfont=dict(family="Courier", size=10))
```

### 2. 子图布局系统

#### make_subplots 基础用法
```python
from plotly.subplots import make_subplots

# 创建2x2子图布局
fig = make_subplots(
    rows=2, cols=2,
    shared_xaxes=True,      # 共享X轴
    shared_yaxes=True,      # 共享Y轴
    vertical_spacing=0.02,   # 垂直间距
    horizontal_spacing=0.02, # 水平间距
    subplot_titles=("Plot 1", "Plot 2", "Plot 3", "Plot 4")
)
```

#### 边缘图布局（Marginal Plots）
```python
# 创建带边缘图的复杂布局
fig = make_subplots(
    rows=2, cols=2,
    column_widths=[0.8, 0.2],    # 主图80%，边缘图20%
    row_heights=[0.2, 0.8],      # 边缘图20%，主图80%
    shared_xaxes=True,
    shared_yaxes=True,
    vertical_spacing=0.02,
    horizontal_spacing=0.02
)

# 主图位置：(2, 1)
# X轴边缘图位置：(1, 1)
# Y轴边缘图位置：(2, 2)
```

### 3. 坐标轴配置

#### 基础坐标轴设置
```python
# 设置坐标轴类型和范围
fig.update_xaxes(
    type="log",              # 对数刻度
    range=[0.1, 1000],       # 范围设置
    title="X轴标题",
    showgrid=True,           # 显示网格线
    gridcolor="lightgray",   # 网格线颜色
    gridwidth=1              # 网格线宽度
)

fig.update_yaxes(
    type="log",
    range=[0.1, 1000],
    title="Y轴标题",
    showgrid=True,
    gridcolor="lightgray",
    gridwidth=1
)
```

#### 子图中的坐标轴配置
```python
# 为特定子图设置坐标轴
fig.update_xaxes(
    title="主图X轴",
    type="log",
    row=2, col=1  # 指定子图位置
)

fig.update_yaxes(
    title="主图Y轴",
    type="log",
    row=2, col=1
)

# 隐藏边缘图的坐标轴标签
fig.update_xaxes(
    showticklabels=False,
    title="",
    row=1, col=1  # X轴边缘图
)

fig.update_yaxes(
    showticklabels=False,
    title="",
    row=2, col=2  # Y轴边缘图
)
```

### 4. 轨迹添加和样式

#### 添加散点图轨迹
```python
# 添加到主图
fig.add_trace(
    go.Scatter(
        x=x_data,
        y=y_data,
        mode='markers',
        marker=dict(
            size=8,
            color=color_data,
            colorscale='Viridis',
            showscale=True,
            colorbar=dict(title="颜色标题")
        ),
        name="数据点"
    ),
    row=2, col=1  # 主图位置
)
```

#### 添加边缘分布图
```python
# X轴边缘直方图
fig.add_trace(
    go.Histogram(
        x=x_data,
        nbinsx=30,
        marker_color='lightblue',
        showlegend=False,
        name=""
    ),
    row=1, col=1
)

# Y轴边缘直方图
fig.add_trace(
    go.Histogram(
        y=y_data,
        nbinsy=30,
        marker_color='lightcoral',
        showlegend=False,
        name="",
        orientation='h'  # 水平方向
    ),
    row=2, col=2
)
```

### 5. 颜色轴和图例

#### 全局颜色轴配置
```python
fig.update_layout(
    coloraxis=dict(
        colorscale='Plasma',
        cmin=-2.0,
        cmax=3.0,
        colorbar=dict(
            title="渗透率 (log mD)",
            titleside="right",
            tickfont=dict(size=10),
            titlefont=dict(size=12)
        )
    )
)
```

#### 图例配置
```python
fig.update_layout(
    legend=dict(
        x=0.02,
        y=0.98,
        bgcolor="rgba(255,255,255,0.8)",
        bordercolor="black",
        borderwidth=1,
        font=dict(size=10)
    ),
    showlegend=True
)
```

## 常见问题和解决方案

### 1. 字体不显示问题
**问题**：设置的字体样式不生效
**解决方案**：
```python
# 确保在 update_layout 中设置全局字体
fig.update_layout(
    font=dict(family="Arial", size=12),
    # 同时设置特定元素的字体
    title_font=dict(size=16),
    legend_font=dict(size=10)
)
```

### 2. 对数刻度不生效
**问题**：设置 `type="log"` 后坐标轴仍为线性刻度
**解决方案**：
```python
# 确保数据中没有零值或负值
# 对数刻度要求所有数据 > 0
fig.update_xaxes(
    type="log",
    range=[np.log10(0.1), np.log10(1000)]  # 使用log10值设置范围
)
```

### 3. 边缘图坐标轴错乱
**问题**：边缘图的坐标轴标题显示在错误位置
**解决方案**：
```python
# 明确指定每个子图的坐标轴配置
# 主图坐标轴
fig.update_xaxes(title="X轴标题", row=2, col=1)
fig.update_yaxes(title="Y轴标题", row=2, col=1)

# 边缘图坐标轴（隐藏标题和刻度标签）
fig.update_xaxes(showticklabels=False, title="", row=1, col=1)
fig.update_yaxes(showticklabels=False, title="", row=2, col=2)
```

## 最佳实践

### 1. 布局设置顺序
```python
# 推荐的设置顺序
fig = make_subplots(...)           # 1. 创建子图布局
fig.add_trace(...)                 # 2. 添加数据轨迹
fig.update_xaxes(...)             # 3. 配置坐标轴
fig.update_yaxes(...)
fig.update_layout(...)            # 4. 设置全局布局
```

### 2. 性能优化
```python
# 批量更新而非逐个设置
layout_updates = {
    'font': dict(family="Arial", size=12),
    'title': dict(text="标题", font=dict(size=16)),
    'showlegend': True,
    'width': 800,
    'height': 600
}
fig.update_layout(**layout_updates)
```

### 3. 响应式设计
```python
# 使用相对尺寸而非固定像素
fig.update_layout(
    width=None,   # 自适应宽度
    height=600,   # 固定高度
    autosize=True # 自动调整大小
)
```

## 交会图边缘图完整实现示例

### 完整的边缘图交会图实现
```python
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np

def create_crossplot_with_marginals(x_data, y_data, color_data=None):
    """创建带边缘图的交会图"""

    # 1. 创建子图布局
    fig = make_subplots(
        rows=2, cols=2,
        column_widths=[0.8, 0.2],
        row_heights=[0.2, 0.8],
        shared_xaxes=True,
        shared_yaxes=True,
        vertical_spacing=0.02,
        horizontal_spacing=0.02
    )

    # 2. 添加主散点图 (row=2, col=1)
    scatter_trace = go.Scatter(
        x=x_data,
        y=y_data,
        mode='markers',
        marker=dict(
            size=6,
            color=color_data if color_data is not None else 'blue',
            colorscale='Plasma' if color_data is not None else None,
            showscale=True if color_data is not None else False,
            colorbar=dict(
                title="Color Scale",
                x=1.02,  # 颜色条位置
                len=0.8
            ) if color_data is not None else None
        ),
        showlegend=False,
        name="Data Points"
    )
    fig.add_trace(scatter_trace, row=2, col=1)

    # 3. 添加X轴边缘直方图 (row=1, col=1)
    fig.add_trace(
        go.Histogram(
            x=x_data,
            nbinsx=30,
            marker_color='lightblue',
            marker_line_color='white',
            marker_line_width=0.5,
            showlegend=False,
            name="",
            hoverinfo='skip'
        ),
        row=1, col=1
    )

    # 4. 添加Y轴边缘直方图 (row=2, col=2)
    fig.add_trace(
        go.Histogram(
            y=y_data,
            nbinsy=30,
            marker_color='lightcoral',
            marker_line_color='white',
            marker_line_width=0.5,
            showlegend=False,
            name="",
            orientation='h',
            hoverinfo='skip'
        ),
        row=2, col=2
    )

    # 5. 配置主图坐标轴
    fig.update_xaxes(
        title="X Axis Title",
        type="log",  # 对数刻度
        showgrid=True,
        gridcolor="lightgray",
        gridwidth=1,
        showline=True,
        linecolor="black",
        linewidth=1,
        row=2, col=1
    )

    fig.update_yaxes(
        title="Y Axis Title",
        type="log",  # 对数刻度
        showgrid=True,
        gridcolor="lightgray",
        gridwidth=1,
        showline=True,
        linecolor="black",
        linewidth=1,
        row=2, col=1
    )

    # 6. 配置边缘图坐标轴（隐藏标签）
    # X轴边缘图
    fig.update_xaxes(
        showticklabels=False,
        title="",
        showgrid=False,
        row=1, col=1
    )
    fig.update_yaxes(
        showticklabels=False,
        title="",
        showgrid=False,
        row=1, col=1
    )

    # Y轴边缘图
    fig.update_xaxes(
        showticklabels=False,
        title="",
        showgrid=False,
        row=2, col=2
    )
    fig.update_yaxes(
        showticklabels=False,
        title="",
        showgrid=False,
        row=2, col=2
    )

    # 7. 全局布局设置
    fig.update_layout(
        title=dict(
            text="Crossplot with Marginal Distributions",
            x=0.5,
            font=dict(size=16)
        ),
        font=dict(
            family="Arial",
            size=12
        ),
        width=800,
        height=700,
        showlegend=False,
        plot_bgcolor='white'
    )

    return fig

# 使用示例
x = np.random.lognormal(0, 1, 1000)
y = np.random.lognormal(0, 1, 1000)
colors = np.random.normal(0, 1, 1000)

fig = create_crossplot_with_marginals(x, y, colors)
fig.show()
```

### 对数刻度边缘图同步
```python
def sync_marginal_scales_with_main(fig, x_data, y_data):
    """确保边缘图的刻度与主图同步"""

    # 获取主图的对数范围
    x_min, x_max = np.min(x_data), np.max(x_data)
    y_min, y_max = np.min(y_data), np.max(y_data)

    # 设置主图范围
    fig.update_xaxes(
        range=[np.log10(x_min * 0.8), np.log10(x_max * 1.2)],
        row=2, col=1
    )
    fig.update_yaxes(
        range=[np.log10(y_min * 0.8), np.log10(y_max * 1.2)],
        row=2, col=1
    )

    # 边缘图也使用对数刻度
    fig.update_xaxes(type="log", row=1, col=1)
    fig.update_yaxes(type="log", row=2, col=2)

    return fig
```

## PlotProfile到Plotly的翻译机制

### matplotlib风格到plotly的映射
```python
def translate_plot_profile_to_plotly(plot_profile):
    """将PlotProfile配置翻译为plotly布局设置"""

    layout_updates = {}

    # 1. 全局字体设置
    if plot_profile.rc_params:
        font_config = {}
        if "font.family" in plot_profile.rc_params:
            font_config["family"] = plot_profile.rc_params["font.family"]
        if "font.size" in plot_profile.rc_params:
            font_config["size"] = plot_profile.rc_params["font.size"]

        if font_config:
            layout_updates["font"] = font_config

    # 2. 图表尺寸
    if plot_profile.figure_props:
        if "figsize" in plot_profile.figure_props:
            figsize = plot_profile.figure_props["figsize"]
            layout_updates["width"] = figsize[0] * 100  # 转换为像素
            layout_updates["height"] = figsize[1] * 100

        if "dpi" in plot_profile.figure_props:
            # plotly中没有直接的DPI设置，但可以调整尺寸
            pass

    # 3. 标题设置
    if plot_profile.title_props:
        title_config = {}
        if "fontsize" in plot_profile.title_props:
            title_config["font"] = {"size": plot_profile.title_props["fontsize"]}
        if "fontweight" in plot_profile.title_props:
            if "font" not in title_config:
                title_config["font"] = {}
            # matplotlib的fontweight映射
            weight_map = {"bold": "bold", "normal": "normal"}
            title_config["font"]["weight"] = weight_map.get(
                plot_profile.title_props["fontweight"], "normal"
            )

        if title_config:
            layout_updates["title"] = title_config

    # 4. 艺术家属性翻译
    artist_updates = {}
    if plot_profile.artist_props:
        # 散点图样式
        if "scatter" in plot_profile.artist_props:
            scatter_props = plot_profile.artist_props["scatter"]
            marker_config = {}

            if "s" in scatter_props:  # matplotlib的size参数
                marker_config["size"] = scatter_props["s"] / 5  # 调整比例
            if "alpha" in scatter_props:
                marker_config["opacity"] = scatter_props["alpha"]
            if "edgecolor" in scatter_props:
                marker_config["line"] = {"color": scatter_props["edgecolor"]}
            if "linewidth" in scatter_props:
                if "line" not in marker_config:
                    marker_config["line"] = {}
                marker_config["line"]["width"] = scatter_props["linewidth"]

            artist_updates["scatter_marker"] = marker_config

        # 坐标轴样式
        if "axis" in plot_profile.artist_props:
            axis_props = plot_profile.artist_props["axis"]
            axis_config = {}

            # 网格设置
            if plot_profile.rc_params.get("axes.grid", False):
                axis_config["showgrid"] = True
                if "grid.alpha" in plot_profile.rc_params:
                    # plotly中通过颜色透明度实现
                    alpha = plot_profile.rc_params["grid.alpha"]
                    axis_config["gridcolor"] = f"rgba(128,128,128,{alpha})"

            artist_updates["axis"] = axis_config

    return layout_updates, artist_updates

# 使用示例
def apply_plot_profile_to_figure(fig, plot_profile):
    """将PlotProfile应用到plotly图表"""

    layout_updates, artist_updates = translate_plot_profile_to_plotly(plot_profile)

    # 应用布局更新
    if layout_updates:
        fig.update_layout(**layout_updates)

    # 应用艺术家样式更新
    if "scatter_marker" in artist_updates:
        # 更新所有散点图轨迹
        for trace in fig.data:
            if trace.type == "scatter" and trace.mode == "markers":
                trace.update(marker=artist_updates["scatter_marker"])

    if "axis" in artist_updates:
        axis_config = artist_updates["axis"]
        fig.update_xaxes(**axis_config)
        fig.update_yaxes(**axis_config)

    return fig
```

## 调试和故障排除

### 1. 调试子图布局
```python
def debug_subplot_layout(fig):
    """调试子图布局，显示每个子图的坐标轴信息"""

    print("=== 子图布局调试信息 ===")

    # 打印子图网格信息
    if hasattr(fig, '_grid_ref') and fig._grid_ref:
        print(f"子图网格: {fig._grid_ref}")

    # 打印所有轨迹的子图位置
    for i, trace in enumerate(fig.data):
        xaxis = getattr(trace, 'xaxis', 'x')
        yaxis = getattr(trace, 'yaxis', 'y')
        print(f"轨迹 {i} ({trace.type}): xaxis={xaxis}, yaxis={yaxis}")

    # 打印布局中的坐标轴配置
    layout = fig.layout
    for attr_name in dir(layout):
        if attr_name.startswith(('xaxis', 'yaxis')):
            axis = getattr(layout, attr_name)
            if axis and hasattr(axis, 'title'):
                print(f"{attr_name}: title='{axis.title.text if axis.title else 'None'}', "
                      f"type='{axis.type}', domain={axis.domain}")

# 使用示例
debug_subplot_layout(fig)
```

### 2. 验证坐标轴配置
```python
def validate_axis_config(fig):
    """验证坐标轴配置是否正确"""

    issues = []

    # 检查对数刻度数据
    for trace in fig.data:
        if hasattr(trace, 'x') and trace.x is not None:
            x_data = np.array(trace.x)
            if np.any(x_data <= 0):
                issues.append(f"轨迹包含非正数X值，不适合对数刻度: min={np.min(x_data)}")

        if hasattr(trace, 'y') and trace.y is not None:
            y_data = np.array(trace.y)
            if np.any(y_data <= 0):
                issues.append(f"轨迹包含非正数Y值，不适合对数刻度: min={np.min(y_data)}")

    # 检查坐标轴类型与数据的兼容性
    layout = fig.layout
    for axis_name in ['xaxis', 'yaxis', 'xaxis2', 'yaxis2']:
        axis = getattr(layout, axis_name, None)
        if axis and axis.type == 'log':
            print(f"{axis_name} 设置为对数刻度")

    if issues:
        print("发现的问题:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("坐标轴配置验证通过")

    return len(issues) == 0

# 使用示例
is_valid = validate_axis_config(fig)
```

### 3. 字体配置检查
```python
def check_font_config(fig):
    """检查字体配置是否正确应用"""

    layout = fig.layout

    print("=== 字体配置检查 ===")

    # 全局字体
    if layout.font:
        print(f"全局字体: family='{layout.font.family}', size={layout.font.size}, color='{layout.font.color}'")
    else:
        print("未设置全局字体")

    # 标题字体
    if layout.title and layout.title.font:
        print(f"标题字体: family='{layout.title.font.family}', size={layout.title.font.size}")

    # 坐标轴字体
    for axis_name in ['xaxis', 'yaxis', 'xaxis2', 'yaxis2']:
        axis = getattr(layout, axis_name, None)
        if axis:
            if axis.title and axis.title.font:
                print(f"{axis_name} 标题字体: size={axis.title.font.size}")
            if axis.tickfont:
                print(f"{axis_name} 刻度字体: size={axis.tickfont.size}")

# 使用示例
check_font_config(fig)
```

### 4. 常见错误修复函数
```python
def fix_common_issues(fig):
    """修复常见的plotly图表问题"""

    # 1. 确保所有轨迹都有正确的子图引用
    for trace in fig.data:
        if not hasattr(trace, 'xaxis') or not trace.xaxis:
            trace.xaxis = 'x'
        if not hasattr(trace, 'yaxis') or not trace.yaxis:
            trace.yaxis = 'y'

    # 2. 修复字体配置
    if not fig.layout.font:
        fig.update_layout(font=dict(family="Arial", size=12))

    # 3. 确保坐标轴显示
    fig.update_xaxes(showline=True, linecolor='black', linewidth=1)
    fig.update_yaxes(showline=True, linecolor='black', linewidth=1)

    # 4. 修复边缘图的坐标轴标签
    # 假设边缘图在 (1,1) 和 (2,2) 位置
    try:
        fig.update_xaxes(showticklabels=False, title="", row=1, col=1)
        fig.update_yaxes(showticklabels=False, title="", row=1, col=1)
        fig.update_xaxes(showticklabels=False, title="", row=2, col=2)
        fig.update_yaxes(showticklabels=False, title="", row=2, col=2)
    except:
        pass  # 如果不是边缘图布局，忽略错误

    return fig

# 使用示例
fig = fix_common_issues(fig)
```

### 5. 性能优化检查
```python
def optimize_figure_performance(fig):
    """优化图表性能"""

    # 1. 减少数据点数量（如果太多）
    for i, trace in enumerate(fig.data):
        if hasattr(trace, 'x') and trace.x is not None:
            if len(trace.x) > 10000:
                print(f"轨迹 {i} 有 {len(trace.x)} 个数据点，考虑采样")
                # 可以实现数据采样逻辑

    # 2. 优化悬停信息
    for trace in fig.data:
        if trace.type == 'scatter':
            # 简化悬停模板
            if not trace.hovertemplate:
                trace.hovertemplate = '%{x}<br>%{y}<extra></extra>'

    # 3. 禁用不必要的交互
    fig.update_layout(
        dragmode='pan',  # 默认为平移而非缩放
        showlegend=False if len(fig.data) == 1 else True  # 单轨迹时隐藏图例
    )

    return fig

# 使用示例
fig = optimize_figure_performance(fig)
```

## 参考资源

- [Plotly Python 官方文档](https://plotly.com/python/)
- [子图布局指南](https://plotly.com/python/subplots/)
- [坐标轴配置](https://plotly.com/python/axes/)
- [字体和标签设置](https://plotly.com/python/figure-labels/)
- [性能优化指南](https://plotly.com/python/performance/)
- [调试技巧](https://plotly.com/python/troubleshooting/)
